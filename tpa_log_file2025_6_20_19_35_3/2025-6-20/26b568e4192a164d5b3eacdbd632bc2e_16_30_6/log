{"type":0,"info":{"appVersion":"1.2.1","pythonVersion":"1.1.0","scriptVersion":"1.5.22","platformName":"macos","systemArchs":"ARM64","systemVersion":"14.4.1","systemName":"macOS","systemBitness":"32"}}
{"type":1,"step":1,"date":1750408206739}
{"type":1,"step":4,"date":1750408206739}
{"type":1,"step":2,"date":1750408206763}
{"type":1,"step":3,"date":1750408206770}
{"type": 3, "date": 1750408208274, "info": "resolution", "args": {"width": 1440, "height": 932}}
{"type": 3, "date": 1750408208279, "info": "screenedge", "args": {"left": 0, "top": 34, "right": 0, "bottom": 94}}
{"type": 3, "date": 1750408208279, "info": "scale", "args": {"mode": "mac2"}}
{"type": 1, "date": 1750408208421, "step": 5}
{"type": 1, "date": 1750408208421, "step": 9}
{"type": 1, "date": 1750408208422, "step": 10}
{"type": 3, "date": 1750408208422, "info": "taskdata", "args": {"chatcount": 1, "textmsgcount": 1, "filemsgcount": 1}}
{"type": 3, "date": 1750408208721, "info": "info", "args": {"wxversion": "4.1.36"}}
{"type": 1, "date": 1750408208776, "step": 15}
{"type": 1, "date": 1750408210943, "step": 16}
{"type": 3, "date": 1750408212389, "info": "position", "args": {"x": 0, "y": 34, "width": 960, "height": 640, "title": "\u4f01\u4e1a\u5fae\u4fe1", "active": "true"}}
{"type": 2, "date": 1750408212612, "cmd": 20, "args": {"keys": ["command", "1"]}}
{"type": 1, "date": 1750408213094, "step": 20}
{"type": 2, "date": 1750408213095, "cmd": 11, "args": {"x": 30, "y": 62, "times": 5}}
{"type": 3, "date": 1750408215707, "info": "wx_language", "args": {"mode": "Chinese"}}
{"type": 3, "date": 1750408215817, "info": "unmatch", "args": {"img": "/Users/<USER>/Library/Caches/cn.xdf.application.magicassistant/bot_scripts/ltintvgj/gznywlel/hvbzsszb/ieenbvyp/iabbpzgr", "maxval": 0.27245190739631653, "minval": -0.999910295009613, "maxloc": [122, 130], "minloc": [48, 82]}}
{"type": 3, "date": 1750408215901, "info": "appearance", "args": {"mode": "Light"}}
{"type": 2, "date": 1750408215901, "cmd": 10, "args": {"x": 25, "y": 62}}
{"type": 1, "date": 1750408216729, "step": 22}
{"type": 2, "date": 1750408216730, "cmd": 10, "args": {"x": 38, "y": 126}}
{"type": 1, "date": 1750408217709, "step": 28}
{"type": 2, "date": 1750408217709, "cmd": 20, "args": {"keys": ["command", "shift", "f"]}}
{"type": 2, "date": 1750408219133, "cmd": 20, "args": {"keys": ["command", "shift", "f"]}}
{"type": 3, "date": 1750408220096, "info": "err", "args": {"code": 350, "reason": "\u672a\u63a5\u6536\u5230\u7aef\u53e3"}}
{"type": 1, "date": 1750408220097, "step": 30}
{"type": 1, "date": 1750408220342, "step": 40, "args": {"chat_id": "24619456"}}
{"type": 1, "date": 1750408220343, "step": 41, "args": {"name": "\u6893\u6768-GZ228247"}}
{"type": 2, "date": 1750408221023, "cmd": 20, "args": {"keys": ["command", "shift", "f"]}}
{"type": 2, "date": 1750408223558, "cmd": 20, "args": {"keys": ["command", "a"]}}
{"type": 2, "date": 1750408224120, "cmd": 20, "args": {"keys": ["delete"]}}
{"type": 2, "date": 1750408224352, "cmd": 20, "args": {"keys": ["command", "v"]}}
{"type": 2, "date": 1750408224894, "cmd": 10, "args": {"x": 93.0, "y": 132.0}}
{"type": 2, "date": 1750408225341, "cmd": 99, "args": {"secs": 1.3}}
{"type": 2, "date": 1750408226952, "cmd": 10, "args": {"x": 91.0, "y": 196.25}}
{"type": 2, "date": 1750408228981, "cmd": 11, "args": {"x": 342, "y": 70, "times": 3}}
{"type": 2, "date": 1750408230335, "cmd": 20, "args": {"keys": ["command", "c"]}}
{"type": 2, "date": 1750408230842, "cmd": 20, "args": {"keys": ["enter"]}}
{"type": 1, "date": 1750408231598, "step": 60, "args": {"msg_type": 1, "chat_id": "24619456", "msg_id": "d79f41c3b4d745de9e5b42f6ae329c86"}}
{"type": 2, "date": 1750408231928, "cmd": 10, "args": {"x": 352, "y": 608}}
{"type": 2, "date": 1750408232467, "cmd": 20, "args": {"keys": ["command", "a"]}}
{"type": 2, "date": 1750408232985, "cmd": 20, "args": {"keys": ["delete"]}}
{"type": 2, "date": 1750408233368, "cmd": 20, "args": {"keys": ["command", "v"]}}
{"type": 2, "date": 1750408234023, "cmd": 20, "args": {"keys": ["enter"]}}
{"type": 2, "date": 1750408234323, "cmd": 20, "args": {"keys": ["command", "enter"]}}
{"type": 1, "date": 1750408234987, "step": 69, "args": {"msg_type": 1, "chat_id": "24619456", "msg_id": "d79f41c3b4d745de9e5b42f6ae329c86"}}
{"type": 1, "date": 1750408235112, "step": 60, "args": {"msg_type": 2, "chat_id": "24619456", "msg_id": "2b10feb9307b4bc4947028812a39ffa4"}}
{"type": 2, "date": 1750408235113, "cmd": 60, "args": {"status": "start"}}
{"type": 2, "date": 1750408236015, "cmd": 60, "args": {"status": "end"}}
{"type": 2, "date": 1750408236411, "cmd": 20, "args": {"keys": ["command", "c"]}}
{"type": 2, "date": 1750408237142, "cmd": 20, "args": {"keys": ["command", "w"]}}
{"type": 2, "date": 1750408238757, "cmd": 10, "args": {"x": 334, "y": 603}}
{"type": 2, "date": 1750408239296, "cmd": 20, "args": {"keys": ["command", "a"]}}
{"type": 2, "date": 1750408239792, "cmd": 20, "args": {"keys": ["delete"]}}
{"type": 2, "date": 1750408239986, "cmd": 20, "args": {"keys": ["command", "v"]}}
{"type": 2, "date": 1750408240650, "cmd": 20, "args": {"keys": ["enter"]}}
{"type": 2, "date": 1750408240951, "cmd": 20, "args": {"keys": ["command", "enter"]}}
{"type": 1, "date": 1750408241647, "step": 69, "args": {"msg_type": 2, "chat_id": "24619456", "msg_id": "2b10feb9307b4bc4947028812a39ffa4"}}
{"type": 1, "date": 1750408241711, "step": 49, "args": {"chat_id": "24619456"}}
{"type": 2, "date": 1750408241713, "cmd": 99, "args": {"secs": 1.2}}
{"type": 1, "date": 1750408242920, "step": 90}
{"type":1,"step":100,"date":1750408244265,"args":{"code":0}}