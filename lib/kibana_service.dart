import 'dart:convert'; // 导入 dart 内置的 json 转换库
import 'package:dio/dio.dart';
import 'package:dio_cookie_manager/dio_cookie_manager.dart';
import 'package:cookie_jar/cookie_jar.dart';
import 'package:flutter/foundation.dart'; // 用于平台检测
import '../models/filter.dart'; // <--- Import Filter model
import '../models/query_ast.dart';
import 'curl_interceptor.dart'; // 导入 cURL 拦截器

/// KibanaService 负责处理所有与 Kibana API 的网络交互。
/// 把它想象成你在 React 项目中的 `src/api/kibana.js` 文件。
/// 它封装了数据获取的细节，让 UI 组件可以轻松地调用。
class KibanaService {
  late final Dio _dio;
  final _cookieJar = CookieJar();

  KibanaService() {
    _dio = Dio(BaseOptions(
      // 设置基础 URL、连接超时
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
    ));

    // 添加 cURL 拦截器用于调试
    _dio.interceptors.add(CurlInterceptor());

    // 只在非Web平台添加 Cookie 管理器
    // Web环境中，浏览器会自动处理Cookie，不需要手动管理
    if (!kIsWeb) {
      _dio.interceptors.add(CookieManager(_cookieJar));
    }
  }

  /// 获取 Kibana 日志，采用服务器端查询模型。
  Future<Map<String, dynamic>> fetchLogs({
    QueryNode? queryAst, // 使用 AST 替代 filters 和 freeTextQuery
    required DateTime startTime,
    required DateTime endTime,
    required String indexPattern,
    List<dynamic>? searchAfter, // 新增：用于分页
    int pageSize = 500, // 新增：页面大小，默认 500
  }) async {
    // 注释掉旧的授权信息
    // const String username = "p1-k12-h5app-user";
    // const String password = "i1ZjyFwv0/";

    try {
      // 注释掉旧的登录请求
      // print("正在尝试登录 (如果需要)...");
      // await _dio.post(
      //   "https://kibanalb.staff.xdf.cn/api/security/v1/login",
      //   data: {"username": username, "password": password},
      // ).catchError((e) {
      //   // Ignore login errors if we are already logged in (e.g., have a valid cookie)
      //   print("登录可能已完成或失败，继续执行查询: $e");
      // });

      // --- Use passed-in time range ---
      final startTimeEpoch = startTime.millisecondsSinceEpoch;
      final endTimeEpoch = endTime.millisecondsSinceEpoch;

      // --- Add detailed logging for debugging time range issues ---
      print("--- KibanaService: Preparing to fetch logs ---");
      print("Index Pattern: $indexPattern");
      print("Query AST: ${queryAst?.toString() ?? 'None'}");
      print("Start Time: $startTime (${startTimeEpoch}ms)");
      print("End Time:   $endTime (${endTimeEpoch}ms)");
      print("-------------------------------------------------");

      // --- 动态查询构建 ---
      final List<Map<String, dynamic>> filterClauses = [];
      
      // 1. 将时间范围添加为独立的 range 查询
      filterClauses.add({
        "range": {
          "@timestamp": {
            "gte": startTimeEpoch,
            "lte": endTimeEpoch,
            "format": "epoch_millis"
          }
        }
      });
      
      // 2. 从 AST 构建查询
      if (queryAst != null) {
        final queryPart = _buildQueryFromAst(queryAst);
        if (queryPart.isNotEmpty) {
          filterClauses.add(queryPart);
        }
      }

      // 新增：如果索引模式匹配特定格式，则添加固定的筛选条件
      // 这是根据用户提供的 cURL 请求添加的，用于筛选 "report task log"
      if (indexPattern.startsWith('p1-pro-work-wechat-magic')) {
        filterClauses.add({
          "query_string": {
            "analyze_wildcard": true,
            "query": "message: \"report task log\""
          }
        });
      }

      final Map<String, dynamic> searchHeader = {
        "search_type": "query_then_fetch",
        "ignore_unavailable": true,
        "index": indexPattern
      };

      final Map<String, dynamic> searchBody = {
        "query": {
          "bool": {
            "filter": filterClauses,
            "must_not": []
          }
        },
        "script_fields": {},
        "_source": true,
        "aggs": {},
        "from": 0,
        "track_total_hits": true,
        "highlight": {
          "pre_tags": ["@n9e-highlighted-field@"],
          "post_tags": ["@/n9e-highlighted-field@"],
          "fields": {"*": {}},
          "fragment_size": 2147483647
        },
        "size": pageSize,
        "sort": [
          {"@timestamp": {"order": "desc", "unmapped_type": "boolean"}}
        ]
      };

      // --- 新增：如果提供了 searchAfter，则添加到查询体中 ---
      if (searchAfter != null) {
        searchBody['search_after'] = searchAfter;
      }

      final requestBody = '${jsonEncode(searchHeader)}\n${jsonEncode(searchBody)}\n';

      print("正在使用动态查询体查询日志...");
      print("请求参数: $requestBody");
      
      // 使用新的 API 端点
      final response = await _dio.post(
        "https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch",
        data: requestBody,
        options: Options(contentType: 'application/json'),
      );
      
      print("查询成功，状态码: ${response.statusCode}");
      
      // 增加对响应体的详细日志记录，方便调试
      // print("响应体: ${jsonEncode(response.data)}");

      final searchResult = response.data['responses'][0];

      // 检查子查询中是否存在错误
      if (searchResult['error'] != null) {
        final errorDetails = jsonEncode(searchResult['error']);
        print("查询失败: $errorDetails");
        // 抛出一个更具体的错误
        throw Exception('Query failed: ${searchResult['error']['reason']}');
      }

      // 安全地访问 hits
      final hitsData = searchResult['hits'];
      if (hitsData == null || hitsData['hits'] == null) {
          print("警告: 响应中未找到 'hits' 字段或 'hits' 数组。");
          return {
            'logs': [],
            'total': 0,
            'lastSortValues': null,
          }; // 如果没有命中结果，返回空列表
      }
      
      final List<dynamic> hits = hitsData['hits'];
      
      // 处理total字段，它可能是一个整数或者一个包含value字段的对象
      int totalHits;
      if (hitsData['total'] is Map) {
        // ES 7.x+ 格式: {"value": 42, "relation": "eq"}
        totalHits = (hitsData['total'] as Map)['value'] as int;
      } else {
        // ES 6.x 格式: 42
        totalHits = hitsData['total'] as int;
      }

      // 直接传递整个 hit 对象，而不是只传递 _source
      // LogEntry.fromJson 将会处理 _source 和 highlight
      final List<Map<String, dynamic>> hitMaps = List<Map<String, dynamic>>.from(hits);

      // --- 新增：提取最后一条日志的 sort 值，用于下一次分页 ---
      final List<dynamic>? lastSortValues = hits.isNotEmpty 
          ? (hits.last['sort'] as List<dynamic>?)
          : null;

      return {
        'logs': hitMaps, // 返回完整的 hit 列表
        'total': totalHits,
        'lastSortValues': lastSortValues,
      };
    } on DioException catch (e) {
      print("网络请求失败: $e");
      throw Exception('获取日志失败: ${e.message}');
    } catch (e) {
      print("发生未知错误: $e");
      throw Exception('获取日志失败，发生未知错误。');
    }
  }

  Map<String, dynamic> _buildQueryFromAst(QueryNode node) {
    if (node is AndNode) {
      return {
        "bool": {
          "must": [
            _buildQueryFromAst(node.left),
            _buildQueryFromAst(node.right),
          ]
        }
      };
    } else if (node is OrNode) {
      return {
        "bool": {
          "should": [
            _buildQueryFromAst(node.left),
            _buildQueryFromAst(node.right),
          ],
          "minimum_should_match": 1
        }
      };
    } else if (node is NotNode) {
      return {
        "bool": {
          "must_not": [_buildQueryFromAst(node.child)]
        }
      };
    } else if (node is FilterNode) {
      // 定义一组我们从 message 字段中解析出来的特殊字段
      // 当用户搜索这些字段时，我们将在原始 message 文本中进行搜索
      const specialMessageKeys = {
        'email', 'taskId', 'appVersion', 'pythonVersion', 'scriptVersion', 
        'platformName', 'systemArchs', 'systemVersion', 'systemName', 'systemBitness'
      };

      if (specialMessageKeys.contains(node.key)) {
        dynamic value = node.value;
        // 对于非 email 字段，它们在 JSON 中是 "key":"value" 的形式
        // 对于 email 字段，它是 email=value 的形式
        String queryString = node.key == 'email' 
            ? '${node.key}=${value}'
            : '"${node.key}":"$value"';

        return {
          "match_phrase": {
            "message": {
              "query": queryString,
            }
          }
        };
      }

      // 1. 优先处理字段存在性查询: field:*
      if (node.operator == ':' && node.value == '*') {
        return {
          "exists": {"field": node.key}
        };
      }

      // 2. 其次处理通配符查询: field:some*value
      if (node.operator == ':' && (node.value.contains('*') || node.value.contains('?'))) {
        return {
          "wildcard": {
            // 使用 .keyword 来确保对未经分析的原始字符串进行通配符匹配
            "${node.key}.keyword": {
              "value": node.value
              // "case_insensitive": true // Removed: Not supported in ES 6.x
            }
          }
        };
      }

      // 3. 新增：处理精确短语匹配
      if (node.operator == ':') {
        return {
          "match_phrase": {
            // 优先在 .keyword 字段上进行精确匹配
            // 如果字段没有 .keyword 版本，ES 会自动回退到标准字段
            // 这对于 'status' (keyword) 和 'message' (text) 等不同类型的字段都更健壮
            "${node.key}.keyword": {
              "query": node.value
            }
          }
        };
      }

      // 4. 处理范围查询: >, <, >=, <=
      if (['>', '<', '>=', '<='].contains(node.operator)) {
        const opMap = {
          '>': 'gt',
          '<': 'lt',
          '>=': 'gte',
          '<=': 'lte',
        };
        return { "range": { node.key: { opMap[node.operator]!: node.value } } };
      }

      // 5. 最后的默认回退是 multi_match (目前行为)
      // This part is now effectively superseded by the match_phrase logic above for ':' operator
      return {
        "multi_match": {
          "query": node.value,
          "lenient": true,
          "fields": [node.key]
        }
      };
    } else if (node is FreeTextNode) {
      return {
        "match_phrase": {
          "message": {
            "query": node.text,
            "slop": 1 // 允许一定的词语距离，增加灵活性
          }
        }
      };
    }
    return {};
  }

  /// 从 Kibana API 获取所有已保存的索引模式列表。
  Future<List<String>> fetchIndexPatterns() async {
    // 注释掉旧的授权信息
    // const String username = "p1-k12-h5app-user";
    // const String password = "i1ZjyFwv0/";

    try {
      // 注释掉旧的登录请求
      // print("正在为获取索引模式执行主动登录...");
      // await _dio.post(
      //   "https://kibanalb.staff.xdf.cn/api/security/v1/login",
      //   data: {"username": username, "password": password},
      // ).catchError((e) {
      //   print("登录可能已完成或失败，将继续尝试获取索引模式: $e");
      // });
      
      print("正在获取索引模式列表...");
      
      // 由于新的API可能没有提供获取索引模式的端点，我们直接返回一个包含已知索引模式的列表
      // 如果后续有对应的API，可以替换为实际的请求
      final List<String> indexPatterns = ['p1-pro-work-wechat-magic'];
      
      print("使用预定义的索引模式: $indexPatterns");
      return indexPatterns;
      
      // 注释掉旧的API请求
      // final response = await _dio.get(
      //   "https://kibanalb.staff.xdf.cn/s/weixinshengtai/api/saved_objects/_find",
      //   queryParameters: {
      //     'type': 'index-pattern',
      //     'per_page': 100, // 最多获取100个索引模式
      //     'fields': ['title'] // 我们只需要标题字段
      //   },
      // );
      // print("索引模式原始数据获取成功。");

      // final List<dynamic> savedObjects = response.data['saved_objects'];
      // final List<String> indexPatterns = savedObjects
      //     .map((obj) => obj['attributes']['title'] as String)
      //     .toList();
      
      // print("解析出的索引模式: $indexPatterns");

      // // 如果API没有返回任何模式，提供一个备用列表
      // if (indexPatterns.isEmpty) {
      //   print("警告: API未返回任何索引模式，将使用备用列表。");
      //   return ['p1-access-*', 'p1-error-*', 'p1-nginx-*']; // Fallback
      // }

      // return indexPatterns;
    } on DioException catch (e) {
      print("获取索引模式失败: $e. 将使用备用列表。");
      // 发生错误时，返回一个备用列表以保证应用可用性
      return ['p1-pro-work-wechat-magic'];
    } catch (e) {
      print("获取索引模式时发生未知错误: $e. 将使用备用列表。");
      return ['p1-pro-work-wechat-magic'];
    }
  }
}