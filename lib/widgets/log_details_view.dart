import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/log_entry.dart';
import 'message_content_view.dart';
// import 'raw_log_view.dart'; // Removed unused import
import 'package:collection/collection.dart';
import '../utils/log_mapping.dart';

// 定义一个回调函数类型，用于处理筛选操作
typedef OnFilter = void Function(String field, String value, bool isExclusion);

class LogDetailsView extends StatefulWidget {
  final LogEntry log;
  final OnFilter onFilter; // 新增回调
  final bool isMobile; // Add isMobile flag

  const LogDetailsView({
    super.key,
    required this.log,
    required this.onFilter, // 新增回调
    this.isMobile = false, // Default to false
  });

  @override
  State<LogDetailsView> createState() => _LogDetailsViewState();
}

class _LogDetailsViewState extends State<LogDetailsView> {
  // 日志排序相关状态
  bool _isReversed = false;

  // 日志类型过滤
  String? _selectedLogType;
  final List<String> _logTypes = [
    '全部类型',
    '设备信息',
    '步骤',
    '指令',
    '日志',
    '错误'
  ];

  // 过滤后的内容
  List<dynamic>? _filteredContent;

  @override
  void initState() {
    super.initState();
    _selectedLogType = '全部类型';
    _initializeContent();
  }

  List<dynamic>? _originalContent;

  void _initializeContent() {
    final message = widget.log.parsed['message'] as String?;
    if (message == null) {
      _originalContent = [];
      _filteredContent = [];
      return;
    }

    const dataPrefix = 'data=';
    final dataIndex = message.indexOf(dataPrefix);

    if (dataIndex == -1) {
      _originalContent = [];
      _filteredContent = [];
      return;
    }

    final jsonString = message.substring(dataIndex + dataPrefix.length);

    try {
      final decodedJson = jsonDecode(jsonString);

      if (decodedJson is! Map<String, dynamic> || !decodedJson.containsKey('data')) {
        _originalContent = [];
        _filteredContent = [];
        return;
      }
      final dataMap = decodedJson['data'];

      if (dataMap is! Map<String, dynamic> || !dataMap.containsKey('content')) {
        _originalContent = [];
        _filteredContent = [];
        return;
      }
      final content = dataMap['content'];

      if (content is! List) {
        _originalContent = [];
        _filteredContent = [];
        return;
      }

      _originalContent = List.from(content);
      _filteredContent = List.from(content);
      _filterContent();

    } catch (e) {
      _originalContent = [];
      _filteredContent = [];
    }
  }

  // 判断日志是否为错误日志
  bool _isErrorLog(Map<String, dynamic> log) {
    final type = log['type']?.toString();

    // 设备信息、步骤、指令类型的错误判断
    if (type == '1') { // 步骤
      final step = log['step']?.toString();
      final description = stepToText[step];

      // 未知步骤视为错误
      if (description == null) return true;

      // 步骤101（脚本异常退出）明确标记为错误
      if (step == '101') return true;

      // 步骤100（脚本完成退出）不是错误，即使包含错误代码也是正常的退出状态码
      if (step == '100') return false;

    } else if (type == '2') { // 指令
      final cmd = log['cmd']?.toString();
      final description = cmdToText[cmd];
      if (description == null) return true; // 未知指令视为错误

    } else if (type == '3') { // 日志
      final info = log['info']?.toString();

      // 明确标记为错误的日志
      if (info == 'err') return true;

      // 日志类型在映射中找不到，视为错误
      if (logToText[info] == null) return true;

    } else if (type != '0') { // 除了设备信息外，未知类型都视为错误
      return true;
    }

    // 检查是否有错误代码（但排除步骤100的情况，因为它可能包含正常的退出状态码）
    if (log.containsKey('args') && log['args'] is Map && log['args'].containsKey('code')) {
      // 如果是步骤类型且是步骤100（脚本完成退出），则不视为错误
      if (type == '1' && log['step']?.toString() == '100') {
        return false;
      }
      return true;
    }

    return false;
  }

  // 过滤内容
  void _filterContent() {
    if (_originalContent == null) return;

    setState(() {
      if (_selectedLogType == null || _selectedLogType == '全部类型') {
        // 如果没有类型过滤，显示所有日志
        _filteredContent = List.from(_originalContent!);
      } else {
        // 根据类型过滤日志
        _filteredContent = _originalContent!.where((item) {
          if (item is! Map<String, dynamic>) return false;

          // 类型过滤
          final logType = item['type'];
          switch (_selectedLogType) {
            case '设备信息':
              return logType == 0;
            case '步骤':
              return logType == 1 && !_isErrorLog(item); // 排除错误日志
            case '指令':
              return logType == 2 && !_isErrorLog(item); // 排除错误日志
            case '日志':
              return logType == 3 && !_isErrorLog(item); // 排除错误日志
            case '错误':
              return _isErrorLog(item); // 使用统一的错误判断逻辑
            default:
              return true;
          }
        }).toList();
      }
    });
  }

  // 切换日志排序顺序
  void _toggleLogOrder() {
    setState(() {
      _isReversed = !_isReversed;
      if (_filteredContent != null) {
        _filteredContent = _filteredContent!.reversed.toList();
      }
    });
  }

  void _onLogTypeChanged(String? newType) {
    setState(() {
      _selectedLogType = newType;
    });
    _filterContent();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Column(
        children: [
          _buildJsonView(context),
          // const SizedBox(height: 8), // Removed as _buildRawLogView is removed
          // _buildRawLogView(context), // Removed unused method call
        ],
      )
    );
  }

  // 构建可交互的 JSON 树视图
  Widget _buildJsonView(BuildContext context) {
    if (!widget.log.isJson) {
      return const SizedBox.shrink();
    }

    final parsedFields = widget.log.parsedMessage;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade300),
      ),
      constraints: const BoxConstraints(maxHeight: 400),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (parsedFields.isNotEmpty) ...[
              _buildSectionHeader('关键信息 (从 message 解析)'),
              ..._buildJsonRows(parsedFields.map((k, v) => MapEntry(k, v as dynamic)), ''),
              const Divider(height: 16),
            ],
            _buildSectionHeader('Message Content'),
            _buildMessageContentView(),
          ]
        ),
      ),
    );
  }

  Widget _buildMessageContentView() {
    final message = widget.log.parsed['message'] as String?;
    if (message == null) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0),
        child: Text('No message content.', style: TextStyle(fontStyle: FontStyle.italic, fontSize: 10)),
      );
    }

    const dataPrefix = 'data=';
    final dataIndex = message.indexOf(dataPrefix);

    if (dataIndex == -1) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
        child: SelectableText(message, style: const TextStyle(fontFamily: 'monospace', fontSize: 10)),
      );
    }

    final jsonString = message.substring(dataIndex + dataPrefix.length);

    try {
      final decodedJson = jsonDecode(jsonString);

      if (decodedJson is! Map<String, dynamic> || !decodedJson.containsKey('data')) {
        throw const FormatException('Expected "data" key in JSON');
      }
      final dataMap = decodedJson['data'];

      if (dataMap is! Map<String, dynamic> || !dataMap.containsKey('content')) {
         throw const FormatException('Expected "content" key in data');
      }
      final content = dataMap['content'];

      if (content is! List) {
        throw const FormatException('"content" is not a list');
      }

      return Column(
        children: [
          // 添加日志统计信息和控制面板
          if (_filteredContent != null && _filteredContent!.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, size: 16),
                  const SizedBox(width: 8),
                  Text('共 ${_filteredContent!.length}/${_originalContent?.length ?? 0} 条日志',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 10)),
                  const SizedBox(width: 16),
                  Text('错误: ${_filteredContent!.where((item) => item is Map<String, dynamic> && _isErrorLog(item)).length} 条',
                    style: const TextStyle(color: Colors.red, fontSize: 10)),
                  const Spacer(),
                  // 日志类型过滤
                  DropdownButton<String>(
                    value: _selectedLogType,
                    items: _logTypes.map((type) =>
                      DropdownMenuItem(value: type, child: Text(type, style: const TextStyle(fontSize: 10)))
                    ).toList(),
                    onChanged: _onLogTypeChanged,
                    underline: Container(height: 1, color: Colors.grey.shade300),
                    isDense: true,
                  ),
                  const SizedBox(width: 16),
                  TextButton.icon(
                    icon: Icon(_isReversed ? Icons.arrow_downward : Icons.arrow_upward, size: 14),
                    label: Text(_isReversed ? '倒序' : '顺序', style: const TextStyle(fontSize: 10)),
                    onPressed: _toggleLogOrder,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
          ],
          // 显示过滤后的内容
          MessageContentView(content: _filteredContent ?? [], isMobile: widget.isMobile),
        ],
      );

    } catch (e) {
      // If anything goes wrong, display the raw message string for debugging.
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
        child: SelectableText(
          'Could not parse message content. Error: $e\\n\\n$message',
          style: const TextStyle(fontFamily: 'monospace', fontSize: 10, color: Colors.red),
        ),
      );
    }
  }

  // 递归构建 JSON 的每一行
  List<Widget> _buildJsonRows(Map<String, dynamic> json, String prefix, {Set<String> excludeKeys = const {}}) {
    final rows = <Widget>[];
    json.forEach((key, value) {
      if (excludeKeys.contains(key)) return;

      final fullKey = prefix.isEmpty ? key : '$prefix.$key';
      if (value is Map<String, dynamic>) {
        rows.add(
          Padding(
            padding: const EdgeInsets.only(left: 12.0, top: 4.0),
            child: Text('$key:', style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 10)),
          )
        );
        rows.addAll(_buildJsonRows(value, fullKey, excludeKeys: {})); // Do not pass excludeKeys down for nested objects
      } else {
        rows.add(_buildKeyValueRow(fullKey, value));
      }
    });
    return rows;
  }

  // 构建单独的键值对行
  Widget _buildKeyValueRow(String key, dynamic value) {
    final valueString = value.toString();
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {}, // For hover effect
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$key: ',
                style: const TextStyle(
                  color: Color(0xFF6A1B9A), // Deep purple for key
                  fontWeight: FontWeight.w500,
                  fontFamily: 'monospace',
                  fontSize: 10,
                ),
              ),
              Expanded(
                child: Text(
                  valueString,
                  style: const TextStyle(
                    color: Color(0xFF1E88E5), // Blue for value
                    fontFamily: 'monospace',
                    fontSize: 10,
                  ),
                ),
              ),
              _FilterButton(icon: Icons.add_circle_outline, onPressed: () => widget.onFilter(key, valueString, false)),
              const SizedBox(width: 4),
              _FilterButton(icon: Icons.remove_circle_outline, onPressed: () => widget.onFilter(key, valueString, true)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
      child: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 11,
          color: Colors.grey.shade700
        ),
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text, String type) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$type 已复制到剪贴板'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  // Removed _buildRawLogView method
}

// Helper widget for filter buttons
class _FilterButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;

  const _FilterButton({required this.icon, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: Icon(icon, size: 14, color: Colors.grey.shade500),
      ),
    );
  }
}