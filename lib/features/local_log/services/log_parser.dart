import 'dart:convert';
import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import '../models/log_entry.dart';
import '../models/log_session.dart';

class LogParserService {
  Future<String> unzipLogArchive(String zipPath) async {
    final tempDir = await getTemporaryDirectory();
    final destinationDir = Directory(p.join(tempDir.path, 'tpa_log_reader_${DateTime.now().millisecondsSinceEpoch}'));
    if (await destinationDir.exists()) {
      await destinationDir.delete(recursive: true);
    }
    await destinationDir.create(recursive: true);

    try {
      await extractFileToDisk(zipPath, destinationDir.path);
    } catch (e) {
      throw Exception('解压文件失败: $e');
    }
    
    final entities = await destinationDir.list().toList();
    final contentDirs = entities
        .whereType<Directory>()
        .where((d) => !p.basename(d.path).startsWith('__'))
        .toList();

    if (contentDirs.length == 1) {
      return contentDirs.first.path;
    } else if (contentDirs.isNotEmpty) {
      final allAreDateDirs = contentDirs.every((d) => RegExp(r'^\d{4}-\d{1,2}-\d{1,2}$').hasMatch(p.basename(d.path)));
      if (allAreDateDirs) {
        return destinationDir.path;
      }
    }
    
    throw Exception('无法在压缩包中确定唯一的日志根目录。');
  }

  Future<Map<String, List<LogSession>>> scanDirectoryForSessions(String rootPath) async {
    final Map<String, List<LogSession>> sessionsByDate = {};
    final rootDir = Directory(rootPath);

    if (!await rootDir.exists()) {
      throw Exception("选择的目录不存在: $rootPath");
    }

    final dateEntities = await rootDir.list().toList();

    for (var dateEntity in dateEntities) {
      if (dateEntity is Directory) {
        final dateDirName = p.basename(dateEntity.path);
        final sessions = <LogSession>[];
        
        await for (var sessionEntity in dateEntity.list()) {
          if (sessionEntity is Directory) {
            final sessionPath = sessionEntity.path;
            final sessionName = p.basename(sessionPath);
            final logFile = File(p.join(sessionPath, 'log'));

            if (await logFile.exists()) {
              final imagePaths = <String>[];
              const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp'];
              await for (var file in sessionEntity.list()) {
                if (file is File) {
                  final extension = p.extension(file.path).toLowerCase();
                  if (imageExtensions.contains(extension)) {
                    imagePaths.add(file.path);
                  }
                }
              }

              final parts = sessionName.split('_');
              if (parts.length >= 4) {
                 final time = parts.sublist(parts.length - 3).join('_');
                 sessions.add(LogSession(
                   path: sessionPath,
                   date: dateDirName,
                   name: sessionName,
                   time: time,
                   imagePaths: imagePaths,
                 ));
              }
            }
          }
        }

        if (sessions.isNotEmpty) {
          sessions.sort((a, b) => b.time.compareTo(a.time));
          sessionsByDate[dateDirName] = sessions;
        }
      }
    }

    final sortedKeys = sessionsByDate.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    return {for (var k in sortedKeys) k: sessionsByDate[k]!};
  }

  Future<List<Map<String, dynamic>>> parseLogFile(String sessionPath) async {
    final logFile = File(p.join(sessionPath, 'log'));
    if (!await logFile.exists()) {
      throw Exception("Log 文件不存在于: $sessionPath");
    }

    final List<Map<String, dynamic>> entries = [];
    final lines = logFile.openRead().transform(utf8.decoder).transform(const LineSplitter());

    await for (var line in lines) {
      if (line.trim().isNotEmpty) {
        try {
          final json = jsonDecode(line);
          if (json is Map<String, dynamic>) {
            entries.add(json);
          }
        } catch (e) {
          // Ignore lines that are not valid JSON
        }
      }
    }
    return entries;
  }
}
